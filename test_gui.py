"""
GUI界面测试脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from PySide6.QtWidgets import QApplication, QMessageBox
    from PySide6.QtCore import Qt
    
    from src.ui.main_window import MainWindow
    from src.utils.constants import APP_NAME
    
    print("✅ 所有模块导入成功")
    
    def test_gui():
        """测试GUI界面"""
        print("🚀 启动GUI测试...")
        
        # 启用高DPI支持
        if hasattr(Qt, 'AA_EnableHighDpiScaling'):
            QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        if hasattr(Qt, 'AA_UseHighDpiPixmaps'):
            QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName(APP_NAME)
        
        try:
            # 创建主窗口
            main_window = MainWindow()
            main_window.show()
            
            print("✅ 主窗口创建成功")
            print("📱 GUI界面已启动，请检查以下功能:")
            print("   - 左右分栏布局")
            print("   - 左侧Logo区域")
            print("   - 歌曲管理手风琴")
            print("   - API管理手风琴")
            print("   - 上传处理区域")
            print("   - 右侧参数配置面板")
            print("   - 深色主题样式")
            print("\n💡 测试建议:")
            print("   1. 点击手风琴标题测试展开/收起")
            print("   2. 拖拽音频文件到上传区域")
            print("   3. 调节右侧滑块参数")
            print("   4. 测试API管理功能")
            print("   5. 调整窗口大小测试响应式布局")
            
            # 运行应用程序
            return app.exec()
            
        except Exception as e:
            print(f"❌ 主窗口创建失败: {e}")
            import traceback
            traceback.print_exc()
            
            # 显示错误对话框
            msg = QMessageBox()
            msg.setIcon(QMessageBox.Critical)
            msg.setWindowTitle("错误")
            msg.setText(f"GUI启动失败: {str(e)}")
            msg.exec()
            
            return 1
    
    if __name__ == "__main__":
        exit_code = test_gui()
        sys.exit(exit_code)
        
except ImportError as e:
    print(f"❌ 模块导入失败: {e}")
    print("\n🔧 请检查以下依赖:")
    print("   - PySide6: pip install PySide6")
    print("   - 确保所有源文件都已创建")
    print("\n📋 缺失的文件检查:")
    
    required_files = [
        "src/__init__.py",
        "src/ui/__init__.py",
        "src/ui/main_window.py",
        "src/ui/panels/__init__.py",
        "src/ui/panels/left_panel.py",
        "src/ui/panels/right_panel.py",
        "src/ui/accordions/__init__.py",
        "src/ui/accordions/base_accordion.py",
        "src/ui/accordions/song_manager_accordion.py",
        "src/ui/accordions/api_manager_accordion.py",
        "src/ui/components/__init__.py",
        "src/ui/components/upload_widget.py",
        "src/ui/components/slider_widget.py",
        "src/utils/__init__.py",
        "src/utils/constants.py",
        "src/utils/styles.py",
    ]
    
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path} (缺失)")
    
    sys.exit(1)
    
except Exception as e:
    print(f"❌ 未知错误: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
