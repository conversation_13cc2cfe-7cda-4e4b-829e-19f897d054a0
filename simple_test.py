"""
简化的GUI测试脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        # 测试PySide6
        from PySide6.QtWidgets import QApplication
        print("✅ PySide6 导入成功")
        
        # 测试项目模块
        from src.utils.constants import APP_NAME, Colors, Styles
        print("✅ 常量模块导入成功")
        
        from src.utils.styles import StyleManager
        print("✅ 样式管理器导入成功")
        
        from src.ui.main_window import MainWindow
        print("✅ 主窗口模块导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_basic_gui():
    """测试基本GUI创建"""
    print("\n🚀 测试基本GUI创建...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from src.ui.main_window import MainWindow
        
        # 创建应用程序（不显示）
        app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = MainWindow()
        print("✅ 主窗口创建成功")
        
        # 检查窗口属性
        print(f"   窗口标题: {main_window.windowTitle()}")
        print(f"   窗口大小: {main_window.width()}x{main_window.height()}")
        
        # 检查子组件
        if hasattr(main_window, 'left_panel'):
            print("✅ 左侧面板创建成功")
        if hasattr(main_window, 'right_panel'):
            print("✅ 右侧面板创建成功")
        if hasattr(main_window, 'splitter'):
            print("✅ 分割器创建成功")
            
        return True
        
    except Exception as e:
        print(f"❌ GUI创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎵 木偶AI翻唱应用 - GUI测试")
    print("=" * 50)
    
    # 测试导入
    if not test_imports():
        print("\n❌ 模块导入测试失败")
        return 1
    
    # 测试GUI创建
    if not test_basic_gui():
        print("\n❌ GUI创建测试失败")
        return 1
    
    print("\n✅ 所有测试通过！")
    print("\n📋 下一步:")
    print("   1. 运行 'workenv\\Scripts\\python.exe src\\main.py' 启动完整GUI")
    print("   2. 测试界面交互功能")
    print("   3. 检查响应式布局")
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
