"""
GUI第二阶段功能测试脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_gui_phase2():
    """测试第二阶段GUI功能"""
    print("🎵 木偶AI翻唱应用 - 第二阶段功能测试")
    print("=" * 60)
    
    try:
        # 测试PySide6导入
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import Qt
        print("✅ PySide6 导入成功")
        
        # 测试新组件导入
        from src.ui.components.audio_player import AudioPlayer
        print("✅ 音频播放器组件导入成功")
        
        from src.ui.components.waveform_widget import WaveformWidget
        print("✅ 波形显示组件导入成功")
        
        from src.ui.components.progress_widget import ProgressWidget
        print("✅ 进度显示组件导入成功")
        
        from src.ui.components.console_widget import ConsoleWidget
        print("✅ 控制台组件导入成功")
        
        # 测试主窗口
        from src.ui.main_window import MainWindow
        print("✅ 主窗口模块导入成功")
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = MainWindow()
        print("✅ 主窗口创建成功")
        
        # 测试组件创建
        print("\n🔧 测试组件创建...")
        
        # 测试音频播放器
        audio_player = AudioPlayer()
        print("✅ 音频播放器创建成功")
        
        # 测试波形显示
        waveform_widget = WaveformWidget()
        print("✅ 波形显示组件创建成功")
        
        # 测试进度显示
        progress_widget = ProgressWidget()
        print("✅ 进度显示组件创建成功")
        
        # 测试控制台
        console_widget = ConsoleWidget()
        console_widget.add_info("测试信息消息")
        console_widget.add_warning("测试警告消息")
        console_widget.add_error("测试错误消息")
        console_widget.add_success("测试成功消息")
        print("✅ 控制台组件创建成功")
        
        # 显示主窗口
        main_window.show()
        print("✅ 主窗口显示成功")
        
        print("\n📋 第二阶段功能测试完成！")
        print("\n🎯 新增功能:")
        print("   ✅ 音频播放器 - 支持播放/暂停/停止/音量控制")
        print("   ✅ 波形显示 - 支持音频可视化和点击定位")
        print("   ✅ 进度显示 - 支持实时进度追踪和取消操作")
        print("   ✅ 控制台输出 - 支持多级别日志显示和过滤")
        print("   ✅ 集成上传组件 - 拖拽上传后自动加载到播放器")
        
        print("\n💡 测试建议:")
        print("   1. 拖拽音频文件到上传区域")
        print("   2. 测试音频播放器控制")
        print("   3. 点击波形图测试定位功能")
        print("   4. 测试手风琴展开/收起")
        print("   5. 查看API管理控制台输出")
        print("   6. 测试参数滑块调节")
        
        print(f"\n🚀 启动命令: g:/0_Software/DDSP/DDSP6.3/workenv/python.exe src/main.py")
        
        # 运行应用程序
        return app.exec()
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("\n🔧 请检查以下依赖:")
        print("   - PySide6: pip install PySide6")
        print("   - pyqtgraph: pip install pyqtgraph")
        return 1
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = test_gui_phase2()
    sys.exit(exit_code)
